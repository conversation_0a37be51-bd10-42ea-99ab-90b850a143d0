{"config": {"configFile": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/tests/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Downloads/bookbuddy/booktalks-buddy/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": null}, "suites": [{"title": "backward-compatibility.spec.ts", "file": "backward-compatibility.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Backward Compatibility Verification", "file": "backward-compatibility.spec.ts", "line": 12, "column": 6, "specs": [], "suites": [{"title": "Original Import Paths", "file": "backward-compatibility.spec.ts", "line": 27, "column": 8, "specs": [{"title": "should access Books Section through original path", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 7467, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:18.407Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-28e9d-ction-through-original-path-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-28e9d-ction-through-original-path-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-1e9463deb2eea37d94cf", "file": "backward-compatibility.spec.ts", "line": 28, "column": 5}, {"title": "should maintain all Books Section functionality", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 8928, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:18.475Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-87252-Books-Section-functionality-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-87252-Books-Section-functionality-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-711ad92c6519d8d4b397", "file": "backward-compatibility.spec.ts", "line": 44, "column": 5}]}, {"title": "Service Function Availability", "file": "backward-compatibility.spec.ts", "line": 62, "column": 8, "specs": [{"title": "should access personal books service functions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 7946, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:18.503Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-9bec4-nal-books-service-functions-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-9bec4-nal-books-service-functions-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-8910872cefc334a4b53d", "file": "backward-compatibility.spec.ts", "line": 63, "column": 5}, {"title": "should access store requests service functions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 8193, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:18.408Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-7d83c--requests-service-functions-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-7d83c--requests-service-functions-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-cbe2cb649dff5dbe7674", "file": "backward-compatibility.spec.ts", "line": 77, "column": 5}, {"title": "should maintain error handling and user feedback", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "failed", "duration": 4568, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:32.027Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-14731--handling-and-user-feedback-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-14731--handling-and-user-feedback-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-5226f6a0e7d23346d428", "file": "backward-compatibility.spec.ts", "line": 92, "column": 5}]}, {"title": "Data Flow and State Management", "file": "backward-compatibility.spec.ts", "line": 109, "column": 8, "specs": [{"title": "should maintain state across tab switches", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 4617, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:32.048Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-c121c-n-state-across-tab-switches-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-c121c-n-state-across-tab-switches-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-b4938705f1aa9ec58096", "file": "backward-compatibility.spec.ts", "line": 110, "column": 5}, {"title": "should handle loading states correctly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "failed", "duration": 4539, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:32.082Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-556b2-le-loading-states-correctly-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-556b2-le-loading-states-correctly-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-48b9521816b2c3ae4d3c", "file": "backward-compatibility.spec.ts", "line": 126, "column": 5}]}, {"title": "Component Integration", "file": "backward-compatibility.spec.ts", "line": 140, "column": 8, "specs": [{"title": "should integrate with existing navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "failed", "duration": 3860, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:32.444Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-486cb-te-with-existing-navigation-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-486cb-te-with-existing-navigation-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-2f3a41e2c26deca5bec3", "file": "backward-compatibility.spec.ts", "line": 141, "column": 5}, {"title": "should maintain responsive design", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 5174, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:41.504Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-0bcd8--maintain-responsive-design-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-0bcd8--maintain-responsive-design-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-a1f748826b79ee1c3d7d", "file": "backward-compatibility.spec.ts", "line": 159, "column": 5}]}, {"title": "API Integration", "file": "backward-compatibility.spec.ts", "line": 180, "column": 8, "specs": [{"title": "should maintain Google Books API integration", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "failed", "duration": 5172, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:41.498Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-b5c2a-oogle-Books-API-integration-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-b5c2a-oogle-Books-API-integration-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-9af16a42605473e07bb3", "file": "backward-compatibility.spec.ts", "line": 181, "column": 5}, {"title": "should handle API errors gracefully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "failed", "duration": 4331, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\n    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20", "location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "snippet": "\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[90m   at \u001b[39m..\\page-objects\\Authentication.page.ts:49\n\n\u001b[0m \u001b[90m 47 |\u001b[39m \u001b[90m   */\u001b[39m\n \u001b[90m 48 |\u001b[39m   \u001b[36masync\u001b[39m goto() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 51 |\u001b[39m   }\n \u001b[90m 52 |\u001b[39m\u001b[0m\n\u001b[2m    at AuthenticationPage.goto (C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts:49:21)\u001b[22m\n\u001b[2m    at C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\tests\\backward-compatibility.spec.ts:21:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-01T00:15:41.494Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-95631-andle-API-errors-gracefully-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\test-results\\backward-compatibility-Bac-95631-andle-API-errors-gracefully-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Downloads\\bookbuddy\\booktalks-buddy\\tests\\e2e\\page-objects\\Authentication.page.ts", "column": 21, "line": 49}}], "status": "unexpected"}], "id": "5d2b1665e2343755b710-cb6206ba56ca207b6997", "file": "backward-compatibility.spec.ts", "line": 197, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-01T00:15:14.703Z", "duration": 32883.68, "expected": 0, "skipped": 0, "unexpected": 11, "flaky": 0}}